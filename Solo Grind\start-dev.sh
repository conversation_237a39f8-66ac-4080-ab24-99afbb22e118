#!/bin/bash

echo "========================================"
echo "    SoloGrind Development Server"
echo "========================================"
echo ""
echo "Environment: Development"
echo "Port: 8080"
echo "URL: http://localhost:8080"
echo ""

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "[INFO] Installing dependencies..."
    echo "This may take a few minutes..."
    npm install
    if [ $? -ne 0 ]; then
        echo "[ERROR] Failed to install dependencies!"
        exit 1
    fi
    echo "[SUCCESS] Dependencies installed successfully!"
    echo ""
fi

# Check if package.json exists
if [ ! -f "package.json" ]; then
    echo "[ERROR] package.json not found! Make sure you're in the correct directory."
    exit 1
fi

# Start the development server
echo "[INFO] Starting development server..."
echo "[INFO] Please wait while the server initializes..."
echo "[INFO] The application will be available at http://localhost:8080"
echo "[INFO] Press Ctrl+C to stop the server"
echo ""
echo "========================================"
echo ""

npm run dev

# If we get here, the server has stopped
echo ""
echo "[INFO] Development server has stopped."
