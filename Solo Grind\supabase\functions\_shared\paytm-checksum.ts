// Paytm Checksum Utilities for Edge Functions
// This is a simplified implementation for demonstration
// In production, use the official Paytm checksum library

export async function generateChecksum(params: Record<string, any>, key: string): Promise<string> {
  try {
    // Remove checksum if present
    const { CHECKSUMHASH, ...cleanParams } = params
    
    // Sort parameters and create query string
    const sortedKeys = Object.keys(cleanParams).sort()
    const queryString = sortedKeys
      .map(k => `${k}=${cleanParams[k]}`)
      .join('&')
    
    // Append merchant key
    const dataToHash = queryString + key
    
    // Generate SHA-256 hash
    const encoder = new TextEncoder()
    const data = encoder.encode(dataToHash)
    const hashBuffer = await crypto.subtle.digest('SHA-256', data)
    
    // Convert to hex string
    const hashArray = Array.from(new Uint8Array(hashBuffer))
    const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
    
    return hashHex
  } catch (error) {
    console.error('Error generating checksum:', error)
    throw new Error('Failed to generate checksum')
  }
}

export async function verifyChecksum(params: Record<string, any>, key: string, receivedChecksum: string): Promise<boolean> {
  try {
    const generatedChecksum = await generateChecksum(params, key)
    return generatedChecksum === receivedChecksum
  } catch (error) {
    console.error('Error verifying checksum:', error)
    return false
  }
}

export function validatePaytmResponse(response: Record<string, any>): boolean {
  const requiredFields = ['ORDERID', 'TXNID', 'STATUS', 'RESPCODE']
  return requiredFields.every(field => response[field] !== undefined)
}

export function isSuccessfulTransaction(status: string): boolean {
  return status === 'TXN_SUCCESS'
}

export function getPaytmErrorMessage(respCode: string): string {
  const errorCodes: Record<string, string> = {
    '01': 'Transaction Successful',
    '141': 'Transaction failed due to invalid parameters',
    '227': 'Transaction failed due to invalid checksum',
    '330': 'Transaction failed due to invalid merchant',
    '501': 'Transaction failed due to system error',
    '810': 'Transaction cancelled by user',
    '400': 'Transaction failed',
  }
  
  return errorCodes[respCode] || 'Unknown error occurred'
}
