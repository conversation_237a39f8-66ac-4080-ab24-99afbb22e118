// Environment information panel
import { Settings } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { EnvironmentInfo } from './types';

interface EnvironmentPanelProps {
  envInfo: EnvironmentInfo;
}

export function EnvironmentPanel({ envInfo }: EnvironmentPanelProps) {
  return (
    <Card className="bg-black/40 border-white/10">
      <CardHeader className="pb-2">
        <CardTitle className="text-xs text-white/80 flex items-center gap-1">
          <Settings className="h-3 w-3" />
          Environment
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-1">
        <div className="flex items-center justify-between">
          <span className="text-xs text-white/60">Mode:</span>
          <Badge 
            variant={envInfo.isProd ? "destructive" : "secondary"}
            className="text-xs"
          >
            {envInfo.environment}
          </Badge>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-xs text-white/60">Analytics:</span>
          <Badge 
            variant={envInfo.features.analytics ? "default" : "secondary"}
            className="text-xs"
          >
            {envInfo.features.analytics ? 'ON' : 'OFF'}
          </Badge>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-xs text-white/60">Debug:</span>
          <Badge 
            variant={envInfo.features.debugMode ? "default" : "secondary"}
            className="text-xs"
          >
            {envInfo.features.debugMode ? 'ON' : 'OFF'}
          </Badge>
        </div>
      </CardContent>
    </Card>
  );
}
