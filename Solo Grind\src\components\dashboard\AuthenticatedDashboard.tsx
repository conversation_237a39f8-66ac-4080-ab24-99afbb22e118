import { useNavigate } from 'react-router-dom';
import { BottomNav } from '@/components/BottomNav';
import { useAuth } from '@/contexts/AuthContext';
import { useTracking, useTimeTracking } from '@/hooks/useTracking';
import { useUserStats } from './hooks/useUserStats';
import { useActivityData } from './hooks/useActivityData';
import { UserHeader } from './UserHeader';
import { ProgressCards } from './ProgressCards';
import { AchievementsCarousel } from './AchievementsCarousel';
import { StatsCards } from './StatsCards';

export function AuthenticatedDashboard() {
  const { signOut } = useAuth();
  const navigate = useNavigate();
  const { trackButtonClick, trackNavigation } = useTracking();

  // Track time spent on home page
  useTimeTracking('Home');

  // Fetch user data
  const {
    stats,
    isLoadingStats,
    userSettings,
    isLoadingSettings,
    guildMembership,
    guildRank,
  } = useUserStats();

  const {
    streakData,
    weeklyActivities,
    isLoadingWeekly,
    todayActivities,
    isLoadingToday,
    recentAchievements,
  } = useActivityData();

  const handleSignOut = async () => {
    trackButtonClick('sign_out', { location: 'home_page' });
    await signOut();
    trackNavigation('home', 'auth');
    navigate('/auth');
  };

  const statCards = [
    {
      label: "Total Distance",
      value: isLoadingStats ? '...' : `${stats?.total_distance || 0} km`,
      color: "from-electric to-purple"
    },
    {
      label: "Current Streak",
      value: streakData ? `${streakData.currentStreak} days` : '...',
      color: "from-purple to-electric"
    },
    {
      label: "Guild Rank",
      value: guildRank ? `#${guildRank}` : (guildMembership ? '...' : 'No Guild'),
      color: "from-electric to-purple"
    },
  ];

  return (
    <div className="flex-1 flex flex-col">
      <UserHeader 
        level={streakData?.level || 1} 
        onSignOut={handleSignOut} 
      />

      <ProgressCards
        weeklyActivities={weeklyActivities}
        todayActivities={todayActivities}
        userSettings={userSettings}
        isLoadingWeekly={isLoadingWeekly}
        isLoadingToday={isLoadingToday}
        isLoadingSettings={isLoadingSettings}
      />

      <AchievementsCarousel recentAchievements={recentAchievements} />

      <StatsCards statCards={statCards} />

      <BottomNav />
    </div>
  );
}
