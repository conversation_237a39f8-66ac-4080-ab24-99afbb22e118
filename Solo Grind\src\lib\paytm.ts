// Paytm Payment Gateway Integration Utilities
import { getPaytmConfig } from './config';

// Paytm Transaction Status Types
export type PaytmTransactionStatus = 
  | 'TXN_SUCCESS' 
  | 'TXN_FAILURE' 
  | 'PENDING';

// Paytm Payment Modes
export type PaytmPaymentMode = 
  | 'UPI' 
  | 'CC' 
  | 'DC' 
  | 'NB' 
  | 'WALLET' 
  | 'EMI';

// Paytm Transaction Request Interface
export interface PaytmTransactionRequest {
  orderId: string;
  amount: string;
  customerId: string;
  customerEmail?: string;
  customerPhone?: string;
  paymentModes?: PaytmPaymentMode[];
}

// Paytm Transaction Response Interface
export interface PaytmTransactionResponse {
  ORDERID: string;
  TXNID: string;
  TXNAMOUNT: string;
  STATUS: PaytmTransactionStatus;
  RESPCODE: string;
  RESPMSG: string;
  PAYMENTMODE: PaytmPaymentMode;
  BANKNAME?: string;
  BANKTXNID?: string;
  GATEWAYNAME?: string;
  MID: string;
  TXNDATE: string;
  CHECKSUMHASH: string;
  CURRENCY: string;
}

// Paytm Initiate Transaction Response
export interface PaytmInitiateResponse {
  success: boolean;
  txnToken?: string;
  orderId: string;
  amount: string;
  error?: string;
}

// UPI Payment Configuration
export interface UPIConfig {
  enabled: boolean;
  apps: string[];
  intentFlow: boolean;
}

// Paytm Configuration Interface
export interface PaytmConfig {
  merchantId: string;
  isStaging: boolean;
  callbackUrl: string;
  websiteUrl: string;
  industryType: string;
  channelId: string;
}

// Generate unique order ID
export const generateOrderId = (prefix: string = 'ORDER'): string => {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000);
  return `${prefix}_${timestamp}_${random}`;
};

// Validate Paytm configuration
export const validatePaytmConfig = (): boolean => {
  const config = getPaytmConfig();
  return !!(
    config.merchantId &&
    config.callbackUrl &&
    config.websiteUrl &&
    config.industryType
  );
};

// Get Paytm environment URLs
export const getPaytmUrls = () => {
  const config = getPaytmConfig();
  
  if (config.isStaging) {
    return {
      transactionUrl: 'https://securestage.paytmpayments.com/theia/api/v1/showPaymentPage',
      statusUrl: 'https://securestage.paytmpayments.com/v3/order/status',
      jsCheckoutUrl: 'https://securestage.paytmpayments.com/merchantpgpui/checkoutjs/merchants/{MID}.js'
    };
  } else {
    return {
      transactionUrl: 'https://secure.paytmpayments.com/theia/api/v1/showPaymentPage',
      statusUrl: 'https://secure.paytmpayments.com/v3/order/status',
      jsCheckoutUrl: 'https://secure.paytmpayments.com/merchantpgpui/checkoutjs/merchants/{MID}.js'
    };
  }
};

// UPI Configuration
export const UPI_CONFIG: UPIConfig = {
  enabled: true,
  apps: ['paytm', 'phonepe', 'googlepay', 'bhim', 'amazonpay'],
  intentFlow: true
};

// Payment mode configurations
export const PAYMENT_MODES = {
  UPI: {
    name: 'UPI',
    displayName: 'UPI',
    icon: '💳',
    description: 'Pay using any UPI app'
  },
  CC: {
    name: 'CC',
    displayName: 'Credit Card',
    icon: '💳',
    description: 'Pay using Credit Card'
  },
  DC: {
    name: 'DC',
    displayName: 'Debit Card',
    icon: '💳',
    description: 'Pay using Debit Card'
  },
  NB: {
    name: 'NB',
    displayName: 'Net Banking',
    icon: '🏦',
    description: 'Pay using Net Banking'
  },
  WALLET: {
    name: 'WALLET',
    displayName: 'Paytm Wallet',
    icon: '👛',
    description: 'Pay using Paytm Wallet'
  }
} as const;

// Error codes and messages
export const PAYTM_ERROR_CODES = {
  '01': 'Transaction Successful',
  '141': 'Transaction failed due to invalid parameters',
  '227': 'Transaction failed due to invalid checksum',
  '330': 'Transaction failed due to invalid merchant',
  '501': 'Transaction failed due to system error',
  '810': 'Transaction cancelled by user',
  '400': 'Transaction failed',
} as const;

// Utility function to get error message
export const getPaytmErrorMessage = (respCode: string): string => {
  return PAYTM_ERROR_CODES[respCode as keyof typeof PAYTM_ERROR_CODES] || 'Unknown error occurred';
};

// Format amount for Paytm (should be string with 2 decimal places)
export const formatPaytmAmount = (amount: number): string => {
  return amount.toFixed(2);
};

// Validate transaction response
export const validateTransactionResponse = (response: PaytmTransactionResponse): boolean => {
  return !!(
    response.ORDERID &&
    response.TXNID &&
    response.STATUS &&
    response.CHECKSUMHASH
  );
};

// Check if transaction is successful
export const isTransactionSuccessful = (status: PaytmTransactionStatus): boolean => {
  return status === 'TXN_SUCCESS';
};

// Load Paytm JS Checkout script
export const loadPaytmScript = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    const config = getPaytmConfig();
    const urls = getPaytmUrls();
    const scriptUrl = urls.jsCheckoutUrl.replace('{MID}', config.merchantId);
    
    // Check if script is already loaded
    if (document.querySelector(`script[src="${scriptUrl}"]`)) {
      resolve();
      return;
    }
    
    const script = document.createElement('script');
    script.src = scriptUrl;
    script.onload = () => resolve();
    script.onerror = () => reject(new Error('Failed to load Paytm script'));
    document.head.appendChild(script);
  });
};

// Default callback URL generator
export const getDefaultCallbackUrl = (): string => {
  const config = getPaytmConfig();
  const baseUrl = window.location.origin;
  
  if (config.callbackUrl) {
    return config.callbackUrl;
  }
  
  // Generate default callback URL
  if (config.isStaging) {
    return `${baseUrl}/payment/callback`;
  } else {
    return `${baseUrl}/payment/callback`;
  }
};
