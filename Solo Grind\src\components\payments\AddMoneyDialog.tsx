import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Plus, Wallet, CreditCard, Smartphone, AlertCircle } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { usePaytm } from '@/hooks/usePaytm';
import { PaymentMethodSelector } from './PaymentMethodSelector';
import { PaytmPaymentMode, generateOrderId } from '@/lib/paytm';
import { isPaytmEnabled } from '@/lib/config';
import { toast } from 'sonner';

interface AddMoneyDialogProps {
  children?: React.ReactNode;
  onSuccess?: () => void;
}

const addMoneySchema = z.object({
  amount: z.coerce
    .number()
    .min(1, 'Minimum amount is ₹1')
    .max(100000, 'Maximum amount is ₹1,00,000'),
});

const quickAmounts = [100, 500, 1000, 2000, 5000];

export function AddMoneyDialog({ children, onSuccess }: AddMoneyDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedMethods, setSelectedMethods] = useState<PaytmPaymentMode[]>(['UPI']);
  const { user } = useAuth();

  const form = useForm<z.infer<typeof addMoneySchema>>({
    resolver: zodResolver(addMoneySchema),
    defaultValues: {
      amount: 0,
    },
  });

  const { 
    startPayment, 
    openCheckout, 
    verifyPayment,
    resetCheckout,
    isLoading, 
    checkoutConfig,
    isInitiated 
  } = usePaytm({
    onSuccess: (response) => {
      toast.success('Money added successfully!');
      setIsOpen(false);
      form.reset();
      resetCheckout();
      onSuccess?.();
    },
    onFailure: (error) => {
      toast.error(`Payment failed: ${error}`);
    },
    onCancel: () => {
      toast.info('Payment cancelled');
    },
  });

  const onSubmit = async (values: z.infer<typeof addMoneySchema>) => {
    if (!user) {
      toast.error('Please login to add money');
      return;
    }

    if (!isPaytmEnabled()) {
      toast.error('Paytm payments are not enabled');
      return;
    }

    if (selectedMethods.length === 0) {
      toast.error('Please select at least one payment method');
      return;
    }

    try {
      const orderId = generateOrderId('WALLET');
      
      await startPayment({
        orderId,
        amount: values.amount.toString(),
        customerId: user.id,
        customerEmail: user.email,
        paymentModes: selectedMethods,
      });
    } catch (error) {
      console.error('Error initiating payment:', error);
    }
  };

  const handleQuickAmount = (amount: number) => {
    form.setValue('amount', amount);
  };

  const handleOpenCheckout = async () => {
    try {
      await openCheckout();
    } catch (error) {
      console.error('Error opening checkout:', error);
    }
  };

  const handleClose = () => {
    setIsOpen(false);
    form.reset();
    resetCheckout();
  };

  if (!isPaytmEnabled()) {
    return (
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          {children || (
            <Button className="bg-electric hover:bg-purple text-white">
              <Plus className="h-4 w-4 mr-2" />
              Add Money
            </Button>
          )}
        </DialogTrigger>
        <DialogContent className="bg-black/90 border-white/20 text-white">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-yellow-400" />
              Payment Unavailable
            </DialogTitle>
            <DialogDescription className="text-white/60">
              Paytm payments are currently not available. Please try again later.
            </DialogDescription>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogTrigger asChild>
        {children || (
          <Button className="bg-electric hover:bg-purple text-white">
            <Plus className="h-4 w-4 mr-2" />
            Add Money
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="bg-black/90 border-white/20 text-white max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Wallet className="h-5 w-5 text-electric" />
            Add Money to Wallet
          </DialogTitle>
          <DialogDescription className="text-white/60">
            Add money to your wallet using Paytm payment gateway
          </DialogDescription>
        </DialogHeader>

        {!isInitiated ? (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Amount Input */}
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-white">Amount (₹)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Enter amount"
                        className="bg-white/10 border-white/20 text-white placeholder:text-white/50"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Quick Amount Buttons */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-white">Quick Select</label>
                <div className="flex flex-wrap gap-2">
                  {quickAmounts.map((amount) => (
                    <Button
                      key={amount}
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => handleQuickAmount(amount)}
                      className="border-white/20 text-white/80 hover:bg-electric/20 hover:border-electric"
                    >
                      ₹{amount}
                    </Button>
                  ))}
                </div>
              </div>

              <Separator className="bg-white/10" />

              {/* Payment Method Selection */}
              <PaymentMethodSelector
                selectedMethods={selectedMethods}
                onMethodChange={setSelectedMethods}
              />

              {/* Submit Button */}
              <Button
                type="submit"
                disabled={isLoading || selectedMethods.length === 0}
                className="w-full bg-electric hover:bg-purple text-white"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Initiating Payment...
                  </>
                ) : (
                  <>
                    <CreditCard className="h-4 w-4 mr-2" />
                    Proceed to Payment
                  </>
                )}
              </Button>
            </form>
          </Form>
        ) : (
          /* Checkout Initiated */
          <div className="space-y-4">
            <div className="text-center space-y-2">
              <div className="p-4 bg-green-500/20 border border-green-500/50 rounded-lg">
                <h3 className="font-semibold text-green-300">Payment Initiated</h3>
                <p className="text-sm text-green-200/80">
                  Amount: ₹{checkoutConfig?.amount}
                </p>
                <p className="text-xs text-green-200/60">
                  Order ID: {checkoutConfig?.orderId}
                </p>
              </div>
            </div>

            <div className="flex gap-2">
              <Button
                onClick={handleOpenCheckout}
                disabled={isLoading}
                className="flex-1 bg-electric hover:bg-purple text-white"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Opening...
                  </>
                ) : (
                  <>
                    <Smartphone className="h-4 w-4 mr-2" />
                    Pay Now
                  </>
                )}
              </Button>
              
              <Button
                variant="outline"
                onClick={handleClose}
                className="border-white/20 text-white/80 hover:bg-white/10"
              >
                Cancel
              </Button>
            </div>

            <div className="text-xs text-white/50 text-center">
              You will be redirected to Paytm payment gateway
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
