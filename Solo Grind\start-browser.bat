@echo off
echo ========================================
echo    SoloGrind Development Server
echo    (Auto-open Browser Mode)
echo ========================================
echo.

REM Check if node_modules exists
if not exist "node_modules" (
    echo [INFO] Installing dependencies...
    echo This may take a few minutes...
    npm install
    if errorlevel 1 (
        echo [ERROR] Failed to install dependencies!
        pause
        exit /b 1
    )
    echo [SUCCESS] Dependencies installed successfully!
    echo.
)

REM Check if package.json exists
if not exist "package.json" (
    echo [ERROR] package.json not found! Make sure you're in the correct directory.
    pause
    exit /b 1
)

REM Start the development server in background
echo [INFO] Starting development server in background...
start /B npm run dev

REM Wait for server to start
echo [INFO] Waiting for server to initialize...
echo [INFO] This usually takes 5-10 seconds...
timeout /t 8 /nobreak > nul

REM Check if server is running by attempting to connect
echo [INFO] Checking if server is ready...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8080' -TimeoutSec 5 -UseBasicParsing; if ($response.StatusCode -eq 200) { Write-Host '[SUCCESS] Server is ready!' } else { Write-Host '[WARNING] Server may still be starting...' } } catch { Write-Host '[WARNING] Server may still be starting...' }"

REM Open browser
echo [INFO] Opening browser...
start http://localhost:8080

echo.
echo ========================================
echo [SUCCESS] Development server is running!
echo URL: http://localhost:8080
echo.
echo To stop the server:
echo 1. Close this window, or
echo 2. Press Ctrl+C in the server window
echo ========================================
echo.

REM Keep the window open
pause
