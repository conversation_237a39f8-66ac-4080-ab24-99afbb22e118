import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  CreditCard, 
  Smartphone, 
  Building2, 
  Wallet,
  CheckCircle2,
  Star
} from 'lucide-react';
import { PaytmPaymentMode, PAYMENT_MODES } from '@/lib/paytm';
import { cn } from '@/lib/utils';

interface PaymentMethodSelectorProps {
  selectedMethods: PaytmPaymentMode[];
  onMethodChange: (methods: PaytmPaymentMode[]) => void;
  className?: string;
}

const paymentMethodIcons = {
  UPI: Smartphone,
  CC: CreditCard,
  DC: CreditCard,
  NB: Building2,
  WALLET: Wallet,
} as const;

const paymentMethodColors = {
  UPI: 'bg-blue-500/20 border-blue-500/50 text-blue-300',
  CC: 'bg-purple-500/20 border-purple-500/50 text-purple-300',
  DC: 'bg-green-500/20 border-green-500/50 text-green-300',
  NB: 'bg-orange-500/20 border-orange-500/50 text-orange-300',
  WALLET: 'bg-pink-500/20 border-pink-500/50 text-pink-300',
} as const;

export function PaymentMethodSelector({ 
  selectedMethods, 
  onMethodChange, 
  className 
}: PaymentMethodSelectorProps) {
  const [hoveredMethod, setHoveredMethod] = useState<PaytmPaymentMode | null>(null);

  const toggleMethod = (method: PaytmPaymentMode) => {
    if (selectedMethods.includes(method)) {
      onMethodChange(selectedMethods.filter(m => m !== method));
    } else {
      onMethodChange([...selectedMethods, method]);
    }
  };

  const selectAllMethods = () => {
    onMethodChange(Object.keys(PAYMENT_MODES) as PaytmPaymentMode[]);
  };

  const clearAllMethods = () => {
    onMethodChange([]);
  };

  return (
    <Card className={cn("bg-black/40 border-white/10", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-white flex items-center gap-2">
              <CreditCard className="h-5 w-5 text-electric" />
              Payment Methods
            </CardTitle>
            <CardDescription className="text-white/60">
              Choose your preferred payment methods
            </CardDescription>
          </div>
          <Badge variant="secondary" className="bg-electric/20 text-electric">
            {selectedMethods.length} selected
          </Badge>
        </div>
        
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={selectAllMethods}
            className="border-white/20 text-white/80 hover:bg-white/10"
          >
            Select All
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={clearAllMethods}
            className="border-white/20 text-white/80 hover:bg-white/10"
          >
            Clear All
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-3">
        {/* UPI - Featured */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Star className="h-4 w-4 text-yellow-400" />
            <span className="text-sm font-medium text-white/80">Recommended</span>
          </div>
          
          <Card
            className={cn(
              "cursor-pointer transition-all duration-200 border-2",
              selectedMethods.includes('UPI')
                ? "bg-blue-500/20 border-blue-500 shadow-lg shadow-blue-500/20"
                : "bg-white/5 border-white/10 hover:border-blue-500/50 hover:bg-blue-500/10",
              hoveredMethod === 'UPI' && "scale-[1.02]"
            )}
            onClick={() => toggleMethod('UPI')}
            onMouseEnter={() => setHoveredMethod('UPI')}
            onMouseLeave={() => setHoveredMethod(null)}
          >
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className={cn(
                    "p-2 rounded-lg",
                    paymentMethodColors.UPI
                  )}>
                    <Smartphone className="h-5 w-5" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-white">UPI</h3>
                    <p className="text-sm text-white/60">
                      Pay using any UPI app (GPay, PhonePe, Paytm, etc.)
                    </p>
                  </div>
                </div>
                {selectedMethods.includes('UPI') && (
                  <CheckCircle2 className="h-5 w-5 text-blue-400" />
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        <Separator className="bg-white/10" />

        {/* Other Payment Methods */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          {(Object.entries(PAYMENT_MODES) as [PaytmPaymentMode, typeof PAYMENT_MODES[PaytmPaymentMode]][])
            .filter(([method]) => method !== 'UPI')
            .map(([method, config]) => {
              const Icon = paymentMethodIcons[method];
              const isSelected = selectedMethods.includes(method);
              
              return (
                <Card
                  key={method}
                  className={cn(
                    "cursor-pointer transition-all duration-200 border",
                    isSelected
                      ? "bg-white/10 border-electric shadow-lg shadow-electric/20"
                      : "bg-white/5 border-white/10 hover:border-white/30 hover:bg-white/10",
                    hoveredMethod === method && "scale-[1.02]"
                  )}
                  onClick={() => toggleMethod(method)}
                  onMouseEnter={() => setHoveredMethod(method)}
                  onMouseLeave={() => setHoveredMethod(null)}
                >
                  <CardContent className="p-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className={cn(
                          "p-1.5 rounded",
                          paymentMethodColors[method]
                        )}>
                          <Icon className="h-4 w-4" />
                        </div>
                        <div>
                          <h4 className="font-medium text-white text-sm">
                            {config.displayName}
                          </h4>
                          <p className="text-xs text-white/50">
                            {config.description}
                          </p>
                        </div>
                      </div>
                      {isSelected && (
                        <CheckCircle2 className="h-4 w-4 text-electric" />
                      )}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
        </div>

        {selectedMethods.length === 0 && (
          <div className="text-center py-4">
            <p className="text-white/60 text-sm">
              Please select at least one payment method to continue
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
