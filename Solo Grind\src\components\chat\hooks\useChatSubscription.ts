import { useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { MessageWithProfile, GuildMessageWithProfile } from '../types';

interface UseChatSubscriptionProps {
  activeTab: 'global' | 'guild';
  guildId?: string;
  onGlobalMessage: (message: MessageWithProfile) => void;
  onGuildMessage: (message: GuildMessageWithProfile) => void;
}

export function useChatSubscription({ 
  activeTab, 
  guildId, 
  onGlobalMessage, 
  onGuildMessage 
}: UseChatSubscriptionProps) {
  useEffect(() => {
    let globalSubscription: any = null;
    let guildSubscription: any = null;

    // Subscribe to global messages
    if (activeTab === 'global') {
      globalSubscription = supabase
        .channel('global-messages')
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'messages',
          },
          async (payload) => {
            // Fetch the complete message with profile data
            const { data } = await supabase
              .from('messages')
              .select('*, profiles(username, avatar_url)')
              .eq('id', payload.new.id)
              .single();

            if (data) {
              onGlobalMessage(data as MessageWithProfile);
            }
          }
        )
        .subscribe();
    }

    // Subscribe to guild messages
    if (activeTab === 'guild' && guildId) {
      guildSubscription = supabase
        .channel(`guild-messages-${guildId}`)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'guild_messages',
            filter: `guild_id=eq.${guildId}`,
          },
          async (payload) => {
            // Fetch the complete message with profile data
            const { data } = await supabase
              .from('guild_messages')
              .select('*, profiles(username, avatar_url)')
              .eq('id', payload.new.id)
              .single();

            if (data) {
              onGuildMessage(data as GuildMessageWithProfile);
            }
          }
        )
        .subscribe();
    }

    return () => {
      if (globalSubscription) {
        supabase.removeChannel(globalSubscription);
      }
      if (guildSubscription) {
        supabase.removeChannel(guildSubscription);
      }
    };
  }, [activeTab, guildId, onGlobalMessage, onGuildMessage]);
}
