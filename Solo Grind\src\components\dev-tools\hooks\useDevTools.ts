// Hook for development tools functionality
import { getEnvironmentInfo } from '@/lib/config';
import { performanceMonitor } from '@/lib/performance';
import { logger } from '@/lib/logger';

export function useDevTools() {
  const envInfo = getEnvironmentInfo();
  const performanceSummary = performanceMonitor.getPerformanceSummary();
  const recentLogs = logger.getLogs().slice(-5);

  const exportData = () => {
    const data = {
      environment: envInfo,
      performance: performanceSummary,
      logs: logger.getLogs(),
      timestamp: new Date().toISOString(),
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `sologrind-dev-data-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return {
    envInfo,
    performanceSummary,
    recentLogs,
    exportData,
  };
}
