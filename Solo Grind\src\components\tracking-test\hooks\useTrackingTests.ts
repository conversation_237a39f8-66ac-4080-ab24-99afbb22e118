// Hook for managing tracking tests
import { useState } from 'react';
import { useTracking } from '@/hooks/useTracking';
import { logger } from '@/lib/logger';
import { analytics } from '@/lib/analytics';
import { TrackingTestResult } from '../types';

export function useTrackingTests() {
  const [testResults, setTestResults] = useState<TrackingTestResult[]>([]);
  const [isRunningTests, setIsRunningTests] = useState(false);
  
  const {
    trackAction,
    trackButtonClick,
    trackWorkoutCompletion,
    trackAchievementUnlock,
    trackGuildActivity,
    trackSubscriptionEvent,
    trackError,
    trackPerformanceMetric,
  } = useTracking();

  const addResult = (message: string, status: TrackingTestResult['status'] = 'success') => {
    setTestResults(prev => [...prev, {
      timestamp: new Date().toLocaleTimeString(),
      message,
      status
    }]);
  };

  const runBasicTrackingTests = async () => {
    try {
      // Test basic tracking
      trackAction('test_action', { test: true });
      addResult('✅ Basic action tracking test completed');

      // Test button click tracking
      trackButtonClick('test_button', { location: 'tracking_test_page' });
      addResult('✅ Button click tracking test completed');

      // Test workout tracking
      trackWorkoutCompletion({
        type: 'Test Run',
        distance: 5.2,
        duration: 1800, // 30 minutes
        calories: 300,
      });
      addResult('✅ Workout tracking test completed');

      // Test achievement tracking
      trackAchievementUnlock({
        achievementId: 'test_achievement',
        achievementName: 'Test Achievement',
        tier: 'Bronze',
      });
      addResult('✅ Achievement tracking test completed');

      // Test guild activity tracking
      trackGuildActivity('test_join', {
        guildId: 'test_guild_123',
        guildName: 'Test Guild',
      });
      addResult('✅ Guild activity tracking test completed');

      // Test subscription tracking
      trackSubscriptionEvent('test_upgrade', {
        plan: 'Pro',
        amount: 1000,
      });
      addResult('✅ Subscription tracking test completed');

      // Test error tracking
      trackError('Test error for tracking verification', {
        component: 'TrackingTest',
        severity: 'low',
      });
      addResult('✅ Error tracking test completed');

      // Test performance tracking
      trackPerformanceMetric('test_load_time', 150, {
        component: 'TrackingTest',
      });
      addResult('✅ Performance tracking test completed');

    } catch (error) {
      addResult(`❌ Basic tracking test failed: ${error}`, 'error');
    }
  };

  const runAnalyticsTests = async () => {
    try {
      // Test analytics initialization
      addResult('🔍 Testing analytics initialization...');
      
      // Test page view tracking
      analytics.trackPageView('/tracking-test');
      addResult('✅ Page view tracking test completed');

      // Test custom event
      analytics.trackEvent('test_custom_event', {
        category: 'Testing',
        label: 'Tracking Test Page',
        value: 1,
      });
      addResult('✅ Custom event tracking test completed');

      // Test user identification
      analytics.identifyUser('test_user_123', {
        email: '<EMAIL>',
        plan: 'free',
      });
      addResult('✅ User identification test completed');

    } catch (error) {
      addResult(`❌ Analytics test failed: ${error}`, 'error');
    }
  };

  const runLoggerTests = async () => {
    try {
      // Test different log levels
      logger.debug('Debug message from tracking test');
      addResult('✅ Debug logging test completed');

      logger.info('Info message from tracking test');
      addResult('✅ Info logging test completed');

      logger.warn('Warning message from tracking test');
      addResult('✅ Warning logging test completed');

      logger.error('Error message from tracking test');
      addResult('✅ Error logging test completed');

      // Test structured logging
      logger.info('Structured log test', {
        component: 'TrackingTest',
        action: 'test_logging',
        metadata: { test: true },
      });
      addResult('✅ Structured logging test completed');

    } catch (error) {
      addResult(`❌ Logger test failed: ${error}`, 'error');
    }
  };

  const runAllTests = async () => {
    setIsRunningTests(true);
    setTestResults([]);
    addResult('🚀 Starting comprehensive tracking system tests...', 'info');

    await runBasicTrackingTests();
    await runAnalyticsTests();
    await runLoggerTests();

    addResult('🎉 All tracking tests completed!', 'info');
    setIsRunningTests(false);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return {
    testResults,
    isRunningTests,
    runAllTests,
    runBasicTrackingTests,
    runAnalyticsTests,
    runLoggerTests,
    clearResults,
  };
}
