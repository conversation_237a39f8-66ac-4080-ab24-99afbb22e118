-- Create guild chat system
-- Migration: Create guild chat system

-- Create guild_messages table for guild-specific chat
CREATE TABLE IF NOT EXISTS public.guild_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    guild_id UUID NOT NULL REFERENCES public.guilds(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    message_type TEXT NOT NULL DEFAULT 'text' CHECK (message_type IN ('text', 'system', 'achievement')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    is_edited BOOLEAN NOT NULL DEFAULT false
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_guild_messages_guild_id ON public.guild_messages(guild_id);
CREATE INDEX IF NOT EXISTS idx_guild_messages_created_at ON public.guild_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_guild_messages_user_id ON public.guild_messages(user_id);

-- Enable Row Level Security
ALTER TABLE public.guild_messages ENABLE ROW LEVEL SECURITY;

-- RLS Policies for guild messages
-- Users can only see messages from guilds they are members of
CREATE POLICY "Guild members can view guild messages" 
ON public.guild_messages FOR SELECT 
TO authenticated
USING (
    EXISTS (
        SELECT 1 
        FROM public.guild_members 
        WHERE guild_members.guild_id = guild_messages.guild_id 
        AND guild_members.user_id = auth.uid()
    )
);

-- Users can only insert messages to guilds they are members of
CREATE POLICY "Guild members can insert guild messages" 
ON public.guild_messages FOR INSERT 
TO authenticated
WITH CHECK (
    auth.uid() = user_id AND
    EXISTS (
        SELECT 1 
        FROM public.guild_members 
        WHERE guild_members.guild_id = guild_messages.guild_id 
        AND guild_members.user_id = auth.uid()
    )
);

-- Users can only update their own messages
CREATE POLICY "Users can update their own messages" 
ON public.guild_messages FOR UPDATE 
TO authenticated
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

-- Users can only delete their own messages (or guild admins can delete any)
CREATE POLICY "Users can delete their own messages or guild admins can delete any" 
ON public.guild_messages FOR DELETE 
TO authenticated
USING (
    auth.uid() = user_id OR
    EXISTS (
        SELECT 1 
        FROM public.guild_members 
        WHERE guild_members.guild_id = guild_messages.guild_id 
        AND guild_members.user_id = auth.uid()
        AND guild_members.role IN ('admin', 'moderator')
    )
);

-- Admins can view all guild messages
CREATE POLICY "Admins can view all guild messages" 
ON public.guild_messages FOR SELECT 
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM public.user_roles
    WHERE user_roles.user_id = auth.uid() AND user_roles.role = 'admin'
  )
);

-- Function to update the updated_at timestamp when a message is edited
CREATE OR REPLACE FUNCTION public.update_guild_message_updated_at()
RETURNS trigger
LANGUAGE plpgsql
AS $$
BEGIN
  NEW.updated_at = now();
  NEW.is_edited = true;
  RETURN NEW;
END;
$$;

-- Trigger to update updated_at on message edits
CREATE TRIGGER on_guild_message_updated
BEFORE UPDATE ON public.guild_messages
FOR EACH ROW
EXECUTE FUNCTION public.update_guild_message_updated_at();

-- Function to send system messages (for achievements, joins, etc.)
CREATE OR REPLACE FUNCTION public.send_guild_system_message(
    p_guild_id UUID,
    p_content TEXT,
    p_message_type TEXT DEFAULT 'system'
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    message_id UUID;
BEGIN
    -- Insert system message (no user_id for system messages)
    INSERT INTO public.guild_messages (guild_id, user_id, content, message_type)
    VALUES (p_guild_id, NULL, p_content, p_message_type)
    RETURNING id INTO message_id;
    
    RETURN message_id;
END;
$$;

-- Function to send achievement messages when users unlock achievements
CREATE OR REPLACE FUNCTION public.send_achievement_message()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    user_guild_id UUID;
    achievement_name TEXT;
    achievement_title TEXT;
    user_name TEXT;
    message_content TEXT;
BEGIN
    -- Get user's guild
    SELECT guild_id INTO user_guild_id
    FROM public.guild_members 
    WHERE user_id = NEW.user_id 
    LIMIT 1;
    
    -- If user is not in a guild, skip
    IF user_guild_id IS NULL THEN
        RETURN NEW;
    END IF;
    
    -- Get achievement details
    SELECT name, title INTO achievement_name, achievement_title
    FROM public.achievements 
    WHERE id = NEW.achievement_id;
    
    -- Get user name
    SELECT display_name INTO user_name
    FROM public.profiles 
    WHERE id = NEW.user_id;
    
    -- Create achievement message
    message_content := user_name || ' unlocked the "' || achievement_title || '" achievement! 🏆';
    
    -- Send the message to the guild
    PERFORM public.send_guild_system_message(user_guild_id, message_content, 'achievement');
    
    RETURN NEW;
END;
$$;

-- Trigger to send achievement messages when users unlock achievements
DROP TRIGGER IF EXISTS on_achievement_unlocked_send_message ON public.user_achievements;
CREATE TRIGGER on_achievement_unlocked_send_message
AFTER INSERT ON public.user_achievements
FOR EACH ROW
EXECUTE FUNCTION public.send_achievement_message();

-- Function to clean up old messages (optional, for maintenance)
CREATE OR REPLACE FUNCTION public.cleanup_old_guild_messages(days_to_keep INTEGER DEFAULT 90)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM public.guild_messages 
    WHERE created_at < (CURRENT_DATE - INTERVAL '1 day' * days_to_keep);
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$;
