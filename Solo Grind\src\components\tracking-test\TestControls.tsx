// Tracking test controls component
import { Activity, Loader2, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { GlassCard } from '@/components/GlassCard';

interface TestControlsProps {
  onRunAllTests: () => void;
  onRunBasicTests: () => void;
  onRunAnalyticsTests: () => void;
  onRunLoggerTests: () => void;
  onClearResults: () => void;
  isRunningTests: boolean;
}

export function TestControls({
  onRunAllTests,
  onRunBasicTests,
  onRunAnalyticsTests,
  onRunLoggerTests,
  onClearResults,
  isRunningTests
}: TestControlsProps) {
  return (
    <GlassCard className="p-6">
      <h2 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
        <Activity className="h-5 w-5" />
        Tracking Tests
      </h2>
      
      <div className="space-y-3">
        <Button
          onClick={onRunAllTests}
          disabled={isRunningTests}
          className="w-full bg-electric hover:bg-purple text-white"
        >
          {isRunningTests ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Running All Tests...
            </>
          ) : (
            'Run All Tests'
          )}
        </Button>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
          <Button
            onClick={onRunBasicTests}
            disabled={isRunningTests}
            variant="outline"
            className="text-white border-white/20 hover:bg-white/10"
          >
            Basic Tracking
          </Button>
          
          <Button
            onClick={onRunAnalyticsTests}
            disabled={isRunningTests}
            variant="outline"
            className="text-white border-white/20 hover:bg-white/10"
          >
            Analytics
          </Button>
          
          <Button
            onClick={onRunLoggerTests}
            disabled={isRunningTests}
            variant="outline"
            className="text-white border-white/20 hover:bg-white/10"
          >
            Logger
          </Button>
        </div>

        <Button
          onClick={onClearResults}
          disabled={isRunningTests}
          variant="outline"
          className="w-full text-white border-white/20 hover:bg-white/10"
        >
          <Trash2 className="h-4 w-4 mr-2" />
          Clear Results
        </Button>
      </div>
    </GlassCard>
  );
}
