// Quick development actions panel
import { <PERSON> } from 'react-router-dom';
import { Database, BarChart3, ExternalLink } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface QuickActionsProps {
  onExportData: () => void;
}

export function QuickActions({ onExportData }: QuickActionsProps) {
  return (
    <Card className="bg-black/40 border-white/10">
      <CardHeader className="pb-2">
        <CardTitle className="text-xs text-white/80">Quick Actions</CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        <div className="grid grid-cols-2 gap-1">
          <Button
            asChild
            variant="outline"
            size="sm"
            className="text-xs h-7 text-white border-white/20 hover:bg-white/10"
          >
            <Link to="/database-test">
              <Database className="h-3 w-3 mr-1" />
              DB Test
            </Link>
          </Button>
          
          <Button
            asChild
            variant="outline"
            size="sm"
            className="text-xs h-7 text-white border-white/20 hover:bg-white/10"
          >
            <Link to="/tracking-test">
              <BarChart3 className="h-3 w-3 mr-1" />
              Analytics
            </Link>
          </Button>
        </div>
        
        <Button
          onClick={onExportData}
          variant="outline"
          size="sm"
          className="w-full text-xs h-7 text-white border-white/20 hover:bg-white/10"
        >
          <ExternalLink className="h-3 w-3 mr-1" />
          Export Data
        </Button>
      </CardContent>
    </Card>
  );
}
