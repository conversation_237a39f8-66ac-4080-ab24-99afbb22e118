import { Play, Square, Loader2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';

interface TrackingControlsProps {
  isTracking: boolean;
  isSaving: boolean;
  onStart: () => void;
  onStop: () => void;
}

export function TrackingControls({ isTracking, isSaving, onStart, onStop }: TrackingControlsProps) {
  return (
    <div className="flex flex-col items-center mb-6">
      {isTracking ? (
        <Button 
          onClick={onStop} 
          size="lg" 
          className="bg-red-600 hover:bg-red-700 text-white font-bold py-4 px-10 rounded-full text-xl shadow-glow"
        >
          <Square className="mr-2" size={24} /> Stop Run
        </Button>
      ) : (
        <Button 
          onClick={onStart} 
          size="lg" 
          className="bg-electric hover:bg-purple text-white font-bold py-4 px-10 rounded-full text-xl shadow-glow" 
          disabled={isSaving}
        >
          {isSaving ? (
            <>
              <Loader2 className="mr-2 h-6 w-6 animate-spin" /> Saving...
            </>
          ) : (
            <>
              <Play className="mr-2" size={24} /> Start Run
            </>
          )}
        </Button>
      )}
    </div>
  );
}
