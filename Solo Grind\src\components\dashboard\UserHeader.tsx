import { Link } from 'react-router-dom';
import { LogOut } from 'lucide-react';
import { AvatarGlow } from '@/components/AvatarGlow';
import { TierBadge } from '@/components/TierBadge';
import { useAuth } from '@/contexts/AuthContext';
import { useTracking } from '@/hooks/useTracking';
import { getTier } from './utils/calculations';

const DEFAULT_AVATAR = "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80";

interface UserHeaderProps {
  level: number;
  onSignOut: () => void;
}

export function UserHeader({ level, onSignOut }: UserHeaderProps) {
  const { profile } = useAuth();

  return (
    <div className="flex items-center justify-between px-3">
      <Link to="/profile" className="flex items-center gap-3 group">
        <AvatarGlow src={profile?.avatar_url || DEFAULT_AVATAR} size={56} className="animate-glow" />
        <div>
          <div className="flex items-center gap-1">
            <span className="font-bold text-lg group-hover:text-electric transition-colors">
              {profile?.username || 'SkyRunner'}
            </span>
            <TierBadge tier={getTier(level)} />
          </div>
          <span className="text-xs text-white/60">Level {level}</span>
        </div>
      </Link>
      <div className="flex items-center gap-2">
        <button 
          onClick={onSignOut} 
          className="glass-card p-2 rounded-full shadow-glow hover:bg-glass/80 border border-white/10"
        >
          <LogOut size={22} className="text-electric" />
        </button>
      </div>
    </div>
  );
}
