// Types for development tools
export interface EnvironmentInfo {
  environment: string;
  isProd: boolean;
  isDev: boolean;
  features: {
    analytics: boolean;
    debugMode: boolean;
    subscriptions: boolean;
  };
}

export interface PerformanceSummary {
  averageLoadTime: number;
  totalRequests: number;
  errorRate: number;
  memoryUsage?: number;
}

export interface LogEntry {
  timestamp: string;
  level: string;
  message: string;
  metadata?: any;
}
