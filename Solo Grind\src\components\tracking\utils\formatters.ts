// Utility functions for formatting tracking data

export const formatDuration = (seconds: number): string => {
  const h = Math.floor(seconds / 3600).toString().padStart(2, '0');
  const m = Math.floor((seconds % 3600) / 60).toString().padStart(2, '0');
  const s = (seconds % 60).toString().padStart(2, '0');
  return `${h}:${m}:${s}`;
};

export const calculateProgress = (current: number, goal: number): number => {
  return goal > 0 ? Math.min((current / goal) * 100, 100) : 0;
};

export const calculateTodayDistance = (
  activities: Array<{ distance_km: number }> | undefined
): number => {
  return activities?.reduce((sum, activity) => sum + Number(activity.distance_km), 0) || 0;
};
