@echo off
echo ========================================
echo    SoloGrind Development Server
echo ========================================
echo.
echo Environment: Development
echo Port: 8080
echo URL: http://localhost:8080
echo.

REM Check if node_modules exists
if not exist "node_modules" (
    echo [INFO] Installing dependencies...
    echo This may take a few minutes...
    npm install
    if errorlevel 1 (
        echo [ERROR] Failed to install dependencies!
        pause
        exit /b 1
    )
    echo [SUCCESS] Dependencies installed successfully!
    echo.
)

REM Check if package.json exists
if not exist "package.json" (
    echo [ERROR] package.json not found! Make sure you're in the correct directory.
    pause
    exit /b 1
)

REM Start the development server
echo [INFO] Starting development server...
echo [INFO] Please wait while the server initializes...
echo [INFO] The application will be available at http://localhost:8080
echo [INFO] Press Ctrl+C to stop the server
echo.
echo ========================================
echo.

npm run dev

REM If we get here, the server has stopped
echo.
echo [INFO] Development server has stopped.
pause
