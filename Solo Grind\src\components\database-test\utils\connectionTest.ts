// Database connection testing utilities
import { supabase } from '@/integrations/supabase/client';
import { TestResult } from '../types';

export const testConnection = async (
  addResult: (result: Omit<TestResult, 'timestamp'>) => void
): Promise<boolean> => {
  const startTime = Date.now();
  try {
    const { data, error } = await supabase.from('profiles').select('count').limit(1);
    const duration = Date.now() - startTime;
    
    if (error) {
      addResult({
        table: 'connection',
        operation: 'ping',
        status: 'error',
        message: `Connection failed: ${error.message}`,
        duration,
      });
      return false;
    }
    
    addResult({
      table: 'connection',
      operation: 'ping',
      status: 'success',
      message: `Connection successful (${duration}ms)`,
      duration,
    });
    return true;
  } catch (error) {
    const duration = Date.now() - startTime;
    addResult({
      table: 'connection',
      operation: 'ping',
      status: 'error',
      message: `Connection error: ${error}`,
      duration,
    });
    return false;
  }
};
