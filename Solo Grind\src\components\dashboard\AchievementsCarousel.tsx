import { GlassCard } from '@/components/GlassCard';
import { getIconComponent } from './utils/icons';

interface Achievement {
  achievements: {
    title: string;
    icon_name: string;
  } | null;
}

interface AchievementsCarouselProps {
  recentAchievements: Achievement[] | undefined;
}

export function AchievementsCarousel({ recentAchievements }: AchievementsCarouselProps) {
  return (
    <div className="mt-5 px-4">
      <h2 className="gradient-title text-lg mb-2">Recent Achievements</h2>
      <div className="flex gap-3 overflow-x-auto scroll-smooth pb-2 snap-x">
        {recentAchievements && recentAchievements.length > 0 ? (
          recentAchievements.map((achievement, index) => (
            <GlassCard
              key={index}
              className="min-w-[110px] snap-center items-center flex flex-col justify-center py-4 shadow-glow hover:scale-105 transition-transform border border-electric/30"
            >
              <div className="w-10 h-10 mb-1 flex items-center justify-center">
                {getIconComponent(achievement.achievements?.icon_name || 'Trophy')}
              </div>
              <span className="text-xs text-white text-center px-1">
                {achievement.achievements?.title || 'Achievement'}
              </span>
            </GlassCard>
          ))
        ) : (
          <GlassCard className="min-w-[110px] snap-center items-center flex flex-col justify-center py-4 opacity-50">
            <div className="w-10 h-10 mb-1 flex items-center justify-center">
              {getIconComponent('Trophy')}
            </div>
            <span className="text-xs text-white/60 text-center px-1">
              No achievements yet
            </span>
          </GlassCard>
        )}
      </div>
    </div>
  );
}
