# Development Environment Configuration
VITE_APP_ENV=development
VITE_APP_NAME=SoloGrind
VITE_APP_VERSION=1.0.0

# Supabase Configuration
VITE_SUPABASE_URL=https://xutzuilymxmhcqhnohwq.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh1dHp1aWx5bXhtaGNxaG5vaHdxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MjkwODAsImV4cCI6MjA2NTUwNTA4MH0.ooqEbQrmVwq7uEPXVkzh11g2_AfgbrEI0EHR2fnxZo4

# Development Features - Enable payments for testing
VITE_ENABLE_SUBSCRIPTIONS=false
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_PAYMENTS=true
VITE_DEBUG_MODE=true

# Analytics Configuration (disabled in development)
VITE_GOOGLE_ANALYTICS_ID=
VITE_MIXPANEL_TOKEN=

# Payment Configuration (enabled for testing)
VITE_STRIPE_PUBLISHABLE_KEY=

# Paytm Test Configuration (Staging)
VITE_PAYTM_MERCHANT_ID=TEST_MERCHANT_ID
VITE_PAYTM_STAGING=true
VITE_PAYTM_CALLBACK_URL=http://localhost:8080/payment/callback
VITE_PAYTM_WEBSITE=WEBSTAGING
VITE_PAYTM_INDUSTRY_TYPE=Retail
VITE_PAYTM_CHANNEL_ID=WEB

# Development Server Configuration
VITE_DEV_SERVER_PORT=8080
VITE_DEV_SERVER_HOST=localhost
