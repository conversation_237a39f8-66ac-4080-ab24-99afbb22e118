import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { formatDistanceToNow } from 'date-fns';
import { Loader2 } from 'lucide-react';
import { MessageWithProfile, GuildMessageWithProfile } from './types';

interface MessageItemProps {
  message: MessageWithProfile | GuildMessageWithProfile;
  isCurrentUser: boolean;
  isGuildMessage?: boolean;
}

export function MessageItem({ message, isCurrentUser, isGuildMessage = false }: MessageItemProps) {
  const isSystemMessage = isGuildMessage && (message as GuildMessageWithProfile).message_type === 'system';
  const isAchievementMessage = isGuildMessage && (message as GuildMessageWithProfile).message_type === 'achievement';
  const isOptimistic = message.id.toString().startsWith('temp-');

  if (isSystemMessage) {
    return (
      <div className="flex justify-center my-2">
        <div className="bg-white/10 px-3 py-1 rounded-full">
          <p className="text-xs text-white/70">{message.content}</p>
        </div>
      </div>
    );
  }

  if (isAchievementMessage) {
    return (
      <div className="flex justify-center my-2">
        <div className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-500/30 px-3 py-2 rounded-lg">
          <p className="text-sm text-yellow-200">{message.content}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex items-start gap-3 ${isCurrentUser ? 'flex-row-reverse' : ''} ${isOptimistic ? 'opacity-70' : ''}`}>
      <Avatar className="h-8 w-8">
        <AvatarImage src={message.profiles?.avatar_url || undefined} />
        <AvatarFallback>{message.profiles?.username?.[0].toUpperCase() || 'U'}</AvatarFallback>
      </Avatar>
      <div className={`flex flex-col ${isCurrentUser ? 'items-end' : 'items-start'}`}>
        <div className={`p-3 rounded-lg max-w-xs md:max-w-md ${isCurrentUser ? 'bg-electric/80' : 'glass-card'} ${isOptimistic ? 'border border-white/20' : ''}`}>
          <p className="text-sm text-white break-words">{message.content}</p>
          {isOptimistic && (
            <div className="flex items-center gap-1 mt-1">
              <Loader2 className="h-3 w-3 animate-spin text-white/50" />
              <span className="text-xs text-white/50">Sending...</span>
            </div>
          )}
        </div>
        <div className="text-2xs text-white/50 mt-1 px-1 flex items-center gap-1">
          {!isCurrentUser && <span className="font-semibold">{message.profiles?.username || 'User'}</span>}
          <span>{formatDistanceToNow(new Date(message.created_at), { addSuffix: true })}</span>
          {isGuildMessage && (message as GuildMessageWithProfile).is_edited && (
            <span className="text-white/40">(edited)</span>
          )}
        </div>
      </div>
    </div>
  );
}
