// Paytm Initiate Transaction Edge Function
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

// Paytm checksum utilities
import { generateChecksum, verifyChecksum } from '../_shared/paytm-checksum.ts'

interface InitiateTransactionRequest {
  amount: number;
  orderId?: string;
  customerId: string;
  customerEmail?: string;
  customerPhone?: string;
  paymentModes?: string[];
}

interface PaytmInitiateParams {
  MID: string;
  WEBSITE: string;
  INDUSTRY_TYPE_ID: string;
  CHANNEL_ID: string;
  ORDER_ID: string;
  CUST_ID: string;
  TXN_AMOUNT: string;
  CALLBACK_URL: string;
  EMAIL?: string;
  MOBILE_NO?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get user from Authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      throw new Error('Missing authorization header')
    }

    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(token)
    
    if (authError || !user) {
      throw new Error('Invalid authentication token')
    }

    // Parse request body
    const { amount, orderId, customerId, customerEmail, customerPhone, paymentModes }: InitiateTransactionRequest = await req.json()

    // Validate required fields
    if (!amount || amount <= 0) {
      throw new Error('Invalid amount')
    }

    if (!customerId) {
      throw new Error('Customer ID is required')
    }

    // Get Paytm configuration from environment
    const PAYTM_MID = Deno.env.get('PAYTM_MERCHANT_ID')
    const PAYTM_KEY = Deno.env.get('PAYTM_MERCHANT_KEY')
    const PAYTM_WEBSITE = Deno.env.get('PAYTM_WEBSITE') || 'WEBSTAGING'
    const PAYTM_INDUSTRY_TYPE = Deno.env.get('PAYTM_INDUSTRY_TYPE') || 'Retail'
    const PAYTM_CHANNEL_ID = Deno.env.get('PAYTM_CHANNEL_ID') || 'WEB'
    const PAYTM_CALLBACK_URL = Deno.env.get('PAYTM_CALLBACK_URL')
    const PAYTM_STAGING = Deno.env.get('PAYTM_STAGING') === 'true'

    if (!PAYTM_MID || !PAYTM_KEY || !PAYTM_CALLBACK_URL) {
      throw new Error('Paytm configuration is incomplete')
    }

    // Generate order ID if not provided
    const finalOrderId = orderId || `ORDER_${Date.now()}_${Math.floor(Math.random() * 1000)}`

    // Get user's wallet
    const { data: wallet, error: walletError } = await supabaseClient
      .from('wallets')
      .select('id')
      .eq('user_id', user.id)
      .single()

    if (walletError || !wallet) {
      throw new Error('User wallet not found')
    }

    // Create payment transaction record
    const { data: paymentTransaction, error: transactionError } = await supabaseClient
      .from('payment_transactions')
      .insert({
        user_id: user.id,
        wallet_id: wallet.id,
        order_id: finalOrderId,
        amount: amount,
        currency: 'INR',
        payment_method: paymentModes?.[0] || 'UPI',
        payment_provider: 'paytm',
        status: 'pending'
      })
      .select()
      .single()

    if (transactionError || !paymentTransaction) {
      throw new Error('Failed to create payment transaction')
    }

    // Prepare Paytm parameters
    const paytmParams: PaytmInitiateParams = {
      MID: PAYTM_MID,
      WEBSITE: PAYTM_WEBSITE,
      INDUSTRY_TYPE_ID: PAYTM_INDUSTRY_TYPE,
      CHANNEL_ID: PAYTM_CHANNEL_ID,
      ORDER_ID: finalOrderId,
      CUST_ID: customerId,
      TXN_AMOUNT: amount.toFixed(2),
      CALLBACK_URL: PAYTM_CALLBACK_URL,
    }

    // Add optional parameters
    if (customerEmail) {
      paytmParams.EMAIL = customerEmail
    }
    if (customerPhone) {
      paytmParams.MOBILE_NO = customerPhone
    }

    // Generate checksum
    const checksum = await generateChecksum(paytmParams, PAYTM_KEY)

    // Prepare request body for Paytm
    const paytmRequestBody = {
      body: {
        requestType: "Payment",
        mid: PAYTM_MID,
        websiteName: PAYTM_WEBSITE,
        orderId: finalOrderId,
        txnAmount: {
          value: amount.toFixed(2),
          currency: "INR",
        },
        userInfo: {
          custId: customerId,
          email: customerEmail,
          mobile: customerPhone,
        },
        callbackUrl: PAYTM_CALLBACK_URL,
        enablePaymentMode: paymentModes || ["UPI", "CC", "DC", "NB", "WALLET"],
      },
      head: {
        signature: checksum,
      },
    }

    // Call Paytm Initiate Transaction API
    const paytmUrl = PAYTM_STAGING 
      ? 'https://securestage.paytmpayments.com/theia/api/v1/initiateTransaction'
      : 'https://secure.paytmpayments.com/theia/api/v1/initiateTransaction'

    const paytmResponse = await fetch(`${paytmUrl}?mid=${PAYTM_MID}&orderId=${finalOrderId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(paytmRequestBody),
    })

    const paytmData = await paytmResponse.json()

    if (!paytmResponse.ok || paytmData.body.resultInfo.resultStatus !== 'S') {
      throw new Error(`Paytm API error: ${paytmData.body.resultInfo.resultMsg}`)
    }

    const txnToken = paytmData.body.txnToken

    // Create Paytm transaction record
    await supabaseClient
      .from('paytm_transactions')
      .insert({
        payment_transaction_id: paymentTransaction.id,
        merchant_id: PAYTM_MID,
        txn_token: txnToken,
      })

    // Return success response
    return new Response(
      JSON.stringify({
        success: true,
        txnToken: txnToken,
        orderId: finalOrderId,
        amount: amount.toFixed(2),
        merchantId: PAYTM_MID,
        isStaging: PAYTM_STAGING,
        callbackUrl: PAYTM_CALLBACK_URL,
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Error in paytm-initiate function:', error)
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})
