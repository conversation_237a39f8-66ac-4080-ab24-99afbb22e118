import { Tables } from '@/integrations/supabase/types';

// Chat message types with profile information
export type MessageWithProfile = Tables<'messages'> & {
  profiles: Pick<Tables<'profiles'>, 'username' | 'avatar_url'> | null;
};

export type GuildMessageWithProfile = Tables<'guild_messages'> & {
  profiles: Pick<Tables<'profiles'>, 'username' | 'avatar_url'> | null;
};

// Chat tab types
export type ChatTab = 'global' | 'guild';

// Guild membership type
export type GuildMembership = {
  guild_id: string;
  guilds: {
    name: string;
    icon_name: string;
  } | null;
};
