-- Remove hardcoded guild data and fix guild system
-- Migration: Remove hardcoded guilds and improve guild system

-- First, remove all existing hardcoded guilds and their members
DELETE FROM public.guild_members;
DELETE FROM public.guilds;

-- Reset the guild system with proper structure
-- Add location columns to guilds table if they don't exist
ALTER TABLE public.guilds 
ADD COLUMN IF NOT EXISTS latitude NUMERIC(10, 8),
ADD COLUMN IF NOT EXISTS longitude NUMERIC(11, 8);

-- Update the create_guild function to handle location
CREATE OR REPLACE FUNCTION public.create_guild(
    p_name TEXT,
    p_icon_name TEXT,
    p_latitude NUMERIC DEFAULT NULL,
    p_longitude NUMERIC DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    new_guild_id UUID;
BEGIN
    -- Create the guild
    INSERT INTO public.guilds (name, icon_name, latitude, longitude, xp)
    VALUES (p_name, p_icon_name, p_latitude, p_longitude, 0)
    RETURNING id INTO new_guild_id;
    
    -- Add the creator as the first member with leader role
    INSERT INTO public.guild_members (guild_id, user_id, role)
    VALUES (new_guild_id, auth.uid(), 'leader');
    
    RETURN new_guild_id;
END;
$$;

-- Function to join a guild
CREATE OR REPLACE FUNCTION public.join_guild(p_guild_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if user is already in a guild
    IF EXISTS (
        SELECT 1 FROM public.guild_members 
        WHERE user_id = auth.uid()
    ) THEN
        RETURN FALSE; -- User already in a guild
    END IF;
    
    -- Add user to the guild
    INSERT INTO public.guild_members (guild_id, user_id, role)
    VALUES (p_guild_id, auth.uid(), 'member');
    
    RETURN TRUE;
END;
$$;

-- Function to leave a guild
CREATE OR REPLACE FUNCTION public.leave_guild()
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    user_guild_id UUID;
    remaining_members INTEGER;
BEGIN
    -- Get user's current guild
    SELECT guild_id INTO user_guild_id
    FROM public.guild_members 
    WHERE user_id = auth.uid();
    
    IF user_guild_id IS NULL THEN
        RETURN FALSE; -- User not in a guild
    END IF;
    
    -- Remove user from guild
    DELETE FROM public.guild_members 
    WHERE user_id = auth.uid();
    
    -- Check if guild is now empty
    SELECT COUNT(*) INTO remaining_members
    FROM public.guild_members 
    WHERE guild_id = user_guild_id;
    
    -- If no members left, delete the guild
    IF remaining_members = 0 THEN
        DELETE FROM public.guilds WHERE id = user_guild_id;
    END IF;
    
    RETURN TRUE;
END;
$$;

-- Function to calculate guild XP based on member activities
CREATE OR REPLACE FUNCTION public.update_guild_xp()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    guild_record RECORD;
    total_xp INTEGER;
BEGIN
    -- Update XP for each guild based on member activities
    FOR guild_record IN 
        SELECT id FROM public.guilds
    LOOP
        -- Calculate total XP from member activities (1 XP per km)
        SELECT COALESCE(SUM(ua.distance_km), 0)::INTEGER INTO total_xp
        FROM public.guild_members gm
        JOIN public.user_activities ua ON gm.user_id = ua.user_id
        WHERE gm.guild_id = guild_record.id;
        
        -- Update guild XP
        UPDATE public.guilds 
        SET xp = total_xp 
        WHERE id = guild_record.id;
    END LOOP;
END;
$$;

-- Trigger to update guild XP when activities are added
CREATE OR REPLACE FUNCTION public.trigger_update_guild_xp()
RETURNS trigger
LANGUAGE plpgsql
AS $$
BEGIN
    PERFORM public.update_guild_xp();
    RETURN NEW;
END;
$$;

DROP TRIGGER IF EXISTS on_activity_update_guild_xp ON public.user_activities;
CREATE TRIGGER on_activity_update_guild_xp
AFTER INSERT OR UPDATE OR DELETE ON public.user_activities
FOR EACH STATEMENT
EXECUTE FUNCTION public.trigger_update_guild_xp();

-- Add RLS policies for guild functions
CREATE POLICY "Users can create guilds" 
ON public.guilds FOR INSERT 
TO authenticated
WITH CHECK (true);

CREATE POLICY "Users can join guilds" 
ON public.guild_members FOR INSERT 
TO authenticated
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can leave their guild" 
ON public.guild_members FOR DELETE 
TO authenticated
USING (auth.uid() = user_id);
