// User information display component
import { User } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { User as AuthUser } from '@supabase/supabase-js';

interface UserInfoProps {
  user: AuthUser;
}

export function UserInfo({ user }: UserInfoProps) {
  return (
    <Card className="bg-glass border-white/10">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-white">
          <User className="h-5 w-5" />
          Current User
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-white/70 text-sm font-mono">
          ID: {user.id}
        </div>
        <div className="text-white/70 text-sm">
          Email: {user.email || 'N/A'}
        </div>
      </CardContent>
    </Card>
  );
}
