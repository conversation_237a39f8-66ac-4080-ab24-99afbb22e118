import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

export function useTrackingData() {
  const { user } = useAuth();

  // Fetch user settings for daily goal
  const { data: userSettings, isLoading: isLoadingSettings } = useQuery({
    queryKey: ['user-settings', user?.id],
    queryFn: async () => {
      if (!user) return null;
      const { data, error } = await supabase
        .from('user_settings')
        .select('daily_goal_km')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching user settings:', error);
        return { daily_goal_km: 6 }; // Default value
      }
      return data;
    },
    enabled: !!user,
  });

  // Fetch today's activities
  const { data: todayActivities, isLoading: isLoadingToday } = useQuery({
    queryKey: ['today-activities', user?.id],
    queryFn: async () => {
      if (!user) return [];
      
      const today = new Date().toISOString().split('T')[0];
      const { data, error } = await supabase
        .from('user_activities')
        .select('distance_km')
        .eq('user_id', user.id)
        .eq('activity_date', today);

      if (error) {
        console.error('Error fetching today activities:', error);
        return [];
      }
      return data;
    },
    enabled: !!user,
  });

  return {
    userSettings,
    isLoadingSettings,
    todayActivities,
    isLoadingToday,
  };
}
