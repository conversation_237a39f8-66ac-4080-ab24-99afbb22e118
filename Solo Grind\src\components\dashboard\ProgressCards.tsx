import { GlassCard } from '@/components/GlassCard';
import { ProgressBar } from '@/components/ProgressBar';
import { calculateProgress, calculateWeeklyDistance, calculateTodayDistance } from './utils/calculations';

interface ProgressCardsProps {
  weeklyActivities: Array<{ distance_km: number }> | undefined;
  todayActivities: Array<{ distance_km: number }> | undefined;
  userSettings: { daily_goal_km: number; weekly_goal_km: number } | null;
  isLoadingWeekly: boolean;
  isLoadingToday: boolean;
  isLoadingSettings: boolean;
}

export function ProgressCards({
  weeklyActivities,
  todayActivities,
  userSettings,
  isLoadingWeekly,
  isLoadingToday,
  isLoadingSettings,
}: ProgressCardsProps) {
  const weeklyDistance = calculateWeeklyDistance(weeklyActivities);
  const todayDistance = calculateTodayDistance(todayActivities);
  const dailyGoal = userSettings?.daily_goal_km || 6;
  const weeklyGoal = userSettings?.weekly_goal_km || 42;

  const weeklyProgress = calculateProgress(weeklyDistance, weeklyGoal);
  const dailyProgress = calculateProgress(todayDistance, dailyGoal);

  return (
    <>
      {/* Weekly Progress */}
      <GlassCard className="mt-6 mx-4 px-5 py-4">
        <div className="flex justify-between items-center mb-2">
          <span className="font-bold text-white/80">Weekly Distance</span>
          <span className="text-xs text-electric font-semibold">
            {isLoadingWeekly || isLoadingSettings ? '...' : `${weeklyDistance.toFixed(1)} / ${weeklyGoal} km`}
          </span>
        </div>
        <ProgressBar percent={isLoadingWeekly || isLoadingSettings ? 0 : weeklyProgress} />
        <div className="flex justify-between text-2xs text-white/60 mt-1">
          <span>Mon</span><span>Sun</span>
        </div>
      </GlassCard>

      {/* Today's Goal Tracker */}
      <GlassCard className="mt-4 mx-4 px-5 py-4">
        <div className="flex justify-between items-center mb-2">
          <span className="font-bold text-white/80">Today's Goal</span>
          <span className="text-xs text-electric font-semibold">
            {isLoadingToday || isLoadingSettings ? '...' : `${todayDistance.toFixed(1)} / ${dailyGoal} km`}
          </span>
        </div>
        <ProgressBar percent={isLoadingToday || isLoadingSettings ? 0 : dailyProgress} />
        <div className="flex justify-between text-2xs text-white/60 mt-1">
          <span>0km</span>
          <span>{isLoadingSettings ? '...' : `${dailyGoal}km`}</span>
        </div>
      </GlassCard>
    </>
  );
}
