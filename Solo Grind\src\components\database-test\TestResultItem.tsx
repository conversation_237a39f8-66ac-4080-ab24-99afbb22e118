// Individual test result display component
import { Check<PERSON>ir<PERSON>, XCircle, Clock } from 'lucide-react';
import { TestResult } from './types';

interface TestResultItemProps {
  result: TestResult;
}

export function TestResultItem({ result }: TestResultItemProps) {
  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return 'text-green-400';
      case 'error':
        return 'text-red-400';
      case 'pending':
        return 'text-yellow-400';
    }
  };

  return (
    <div className="flex items-center justify-between bg-black/20 p-3 rounded">
      <div className="flex items-center gap-3">
        {getStatusIcon(result.status)}
        <div>
          <div className="text-white font-medium">
            {result.table}.{result.operation}
          </div>
          <div className={`text-sm ${getStatusColor(result.status)}`}>
            {result.message}
          </div>
        </div>
      </div>
      <div className="text-right">
        <div className="text-white/60 text-xs">
          {result.timestamp.toLocaleTimeString()}
        </div>
        {result.duration && (
          <div className="text-white/40 text-xs">
            {result.duration}ms
          </div>
        )}
      </div>
    </div>
  );
}
