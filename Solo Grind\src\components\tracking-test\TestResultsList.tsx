// Tracking test results display component
import { CheckCircle, XCircle, Info } from 'lucide-react';
import { GlassCard } from '@/components/GlassCard';
import { TrackingTestResult } from './types';

interface TestResultsListProps {
  testResults: TrackingTestResult[];
}

export function TestResultsList({ testResults }: TestResultsListProps) {
  if (testResults.length === 0) {
    return null;
  }

  const getStatusIcon = (status: TrackingTestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'info':
        return <Info className="h-4 w-4 text-blue-500" />;
    }
  };

  const getStatusColor = (status: TrackingTestResult['status']) => {
    switch (status) {
      case 'success':
        return 'text-green-400';
      case 'error':
        return 'text-red-400';
      case 'info':
        return 'text-blue-400';
    }
  };

  return (
    <GlassCard className="p-6">
      <h3 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
        <CheckCircle className="h-5 w-5" />
        Test Results ({testResults.length})
      </h3>
      <div className="space-y-2 max-h-96 overflow-y-auto">
        {testResults.map((result, index) => (
          <div
            key={index}
            className="flex items-start gap-3 bg-black/20 p-3 rounded"
          >
            {getStatusIcon(result.status)}
            <div className="flex-1">
              <div className={`text-sm ${getStatusColor(result.status)}`}>
                {result.message}
              </div>
              <div className="text-white/40 text-xs mt-1">
                {result.timestamp}
              </div>
            </div>
          </div>
        ))}
      </div>
    </GlassCard>
  );
}
