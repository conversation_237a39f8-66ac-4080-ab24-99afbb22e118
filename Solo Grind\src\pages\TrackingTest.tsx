// Test page for verifying tracking and analytics functionality
import { BottomNav } from '@/components/BottomNav';
import { useAuth } from '@/contexts/AuthContext';
import {
  TestControls,
  TestResultsList,
  EnvironmentInfo,
  useTrackingTests
} from '@/components/tracking-test';

export default function TrackingTest() {
  const { user, profile } = useAuth();
  const {
    testResults,
    isRunningTests,
    runAllTests,
    runBasicTrackingTests,
    runAnalyticsTests,
    runLoggerTests,
    clearResults,
  } = useTrackingTests();

  return (
    <>
      <div
        style={{ backgroundImage: "url('https://images.unsplash.com/photo-1470813740244-df37b8c1edcb')" }}
        className="fixed inset-0 bg-center bg-cover filter blur-sm scale-105"
      />
      <div className="relative z-10 pb-20 pt-6 min-h-screen bg-black/70">
        <div className="px-4 space-y-6">
          <h1 className="gradient-title text-2xl mb-4 text-center">
            Analytics & Tracking Test
          </h1>

          {/* Environment & User Info */}
          <EnvironmentInfo user={user} profile={profile} />

          {/* Test Controls */}
          <TestControls
            onRunAllTests={runAllTests}
            onRunBasicTests={runBasicTrackingTests}
            onRunAnalyticsTests={runAnalyticsTests}
            onRunLoggerTests={runLoggerTests}
            onClearResults={clearResults}
            isRunningTests={isRunningTests}
          />

          {/* Test Results */}
          <TestResultsList testResults={testResults} />
        </div>
        <BottomNav />
      </div>
    </>
  );
}
