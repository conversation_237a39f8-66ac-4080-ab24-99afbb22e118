// Performance monitoring and optimization utilities
import { trackPerformance } from './analytics';
import { logPerformance } from './logger';
import { isDebugMode, debugLog } from './config';

// Performance metrics interface
export interface PerformanceMetric {
  name: string;
  value: number;
  unit: 'ms' | 'bytes' | 'count' | 'percentage';
  timestamp: Date;
  context?: Record<string, any>;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private timers: Map<string, number> = new Map();

  constructor() {
    this.setupPerformanceObserver();
    this.monitorWebVitals();
  }

  // Setup Performance Observer for Web APIs
  private setupPerformanceObserver() {
    if (typeof window === 'undefined' || !window.PerformanceObserver) {
      return;
    }

    try {
      // Monitor navigation timing
      const navObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'navigation') {
            const navEntry = entry as PerformanceNavigationTiming;
            this.recordMetric('page_load_time', navEntry.loadEventEnd - navEntry.navigationStart, 'ms');
            this.recordMetric('dom_content_loaded', navEntry.domContentLoadedEventEnd - navEntry.navigationStart, 'ms');
            this.recordMetric('first_paint', navEntry.responseStart - navEntry.navigationStart, 'ms');
          }
        }
      });
      navObserver.observe({ entryTypes: ['navigation'] });

      // Monitor resource timing
      const resourceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'resource') {
            const resourceEntry = entry as PerformanceResourceTiming;
            this.recordMetric('resource_load_time', resourceEntry.responseEnd - resourceEntry.startTime, 'ms', {
              resource_name: resourceEntry.name,
              resource_type: resourceEntry.initiatorType,
            });
          }
        }
      });
      resourceObserver.observe({ entryTypes: ['resource'] });

      // Monitor largest contentful paint
      const lcpObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordMetric('largest_contentful_paint', entry.startTime, 'ms');
        }
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

      // Monitor first input delay
      const fidObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          const fidEntry = entry as PerformanceEventTiming;
          this.recordMetric('first_input_delay', fidEntry.processingStart - fidEntry.startTime, 'ms');
        }
      });
      fidObserver.observe({ entryTypes: ['first-input'] });

    } catch (error) {
      debugLog('Performance Observer setup failed:', error);
    }
  }

  // Monitor Core Web Vitals
  private monitorWebVitals() {
    if (typeof window === 'undefined') return;

    // Cumulative Layout Shift
    let clsValue = 0;
    let clsEntries: PerformanceEntry[] = [];

    const clsObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (!(entry as any).hadRecentInput) {
          clsEntries.push(entry);
          clsValue += (entry as any).value;
        }
      }
    });

    try {
      clsObserver.observe({ entryTypes: ['layout-shift'] });
      
      // Report CLS when page visibility changes
      document.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'hidden') {
          this.recordMetric('cumulative_layout_shift', clsValue, 'count');
        }
      });
    } catch (error) {
      debugLog('CLS monitoring setup failed:', error);
    }
  }

  // Record a performance metric
  recordMetric(name: string, value: number, unit: PerformanceMetric['unit'], context?: Record<string, any>) {
    const metric: PerformanceMetric = {
      name,
      value,
      unit,
      timestamp: new Date(),
      context,
    };

    this.metrics.push(metric);
    
    // Keep only last 1000 metrics
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }

    // Log and track the metric
    logPerformance(name, value, context);
    trackPerformance(name, value, context);

    if (isDebugMode()) {
      debugLog(`Performance: ${name} = ${value}${unit}`, context);
    }
  }

  // Start a timer
  startTimer(name: string) {
    this.timers.set(name, performance.now());
  }

  // End a timer and record the metric
  endTimer(name: string, context?: Record<string, any>) {
    const startTime = this.timers.get(name);
    if (startTime === undefined) {
      debugLog(`Timer '${name}' was not started`);
      return;
    }

    const duration = performance.now() - startTime;
    this.timers.delete(name);
    this.recordMetric(name, duration, 'ms', context);
    return duration;
  }

  // Measure function execution time
  measureFunction<T>(name: string, fn: () => T, context?: Record<string, any>): T {
    this.startTimer(name);
    try {
      const result = fn();
      this.endTimer(name, context);
      return result;
    } catch (error) {
      this.endTimer(name, { ...context, error: true });
      throw error;
    }
  }

  // Measure async function execution time
  async measureAsyncFunction<T>(name: string, fn: () => Promise<T>, context?: Record<string, any>): Promise<T> {
    this.startTimer(name);
    try {
      const result = await fn();
      this.endTimer(name, context);
      return result;
    } catch (error) {
      this.endTimer(name, { ...context, error: true });
      throw error;
    }
  }

  // Get memory usage (if available)
  getMemoryUsage() {
    if (typeof window !== 'undefined' && (performance as any).memory) {
      const memory = (performance as any).memory;
      this.recordMetric('memory_used', memory.usedJSHeapSize, 'bytes');
      this.recordMetric('memory_total', memory.totalJSHeapSize, 'bytes');
      this.recordMetric('memory_limit', memory.jsHeapSizeLimit, 'bytes');
      
      return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit,
      };
    }
    return null;
  }

  // Get all metrics
  getMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }

  // Get metrics by name
  getMetricsByName(name: string): PerformanceMetric[] {
    return this.metrics.filter(metric => metric.name === name);
  }

  // Clear metrics
  clearMetrics() {
    this.metrics = [];
  }

  // Export metrics as JSON
  exportMetrics(): string {
    return JSON.stringify(this.metrics, null, 2);
  }

  // Get performance summary
  getPerformanceSummary() {
    const summary: Record<string, { count: number; avg: number; min: number; max: number }> = {};
    
    for (const metric of this.metrics) {
      if (!summary[metric.name]) {
        summary[metric.name] = { count: 0, avg: 0, min: Infinity, max: -Infinity };
      }
      
      const s = summary[metric.name];
      s.count++;
      s.min = Math.min(s.min, metric.value);
      s.max = Math.max(s.max, metric.value);
      s.avg = (s.avg * (s.count - 1) + metric.value) / s.count;
    }
    
    return summary;
  }
}

// Create singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Convenience functions
export const recordMetric = (name: string, value: number, unit: PerformanceMetric['unit'], context?: Record<string, any>) => {
  performanceMonitor.recordMetric(name, value, unit, context);
};

export const startTimer = (name: string) => {
  performanceMonitor.startTimer(name);
};

export const endTimer = (name: string, context?: Record<string, any>) => {
  return performanceMonitor.endTimer(name, context);
};

export const measureFunction = <T>(name: string, fn: () => T, context?: Record<string, any>): T => {
  return performanceMonitor.measureFunction(name, fn, context);
};

export const measureAsyncFunction = <T>(name: string, fn: () => Promise<T>, context?: Record<string, any>): Promise<T> => {
  return performanceMonitor.measureAsyncFunction(name, fn, context);
};

// React hook for measuring component render time
export const useMeasureRender = (componentName: string) => {
  const startTime = performance.now();
  
  return () => {
    const renderTime = performance.now() - startTime;
    recordMetric(`${componentName}_render_time`, renderTime, 'ms');
  };
};

// Decorator for measuring method execution time
export const measureMethod = (name?: string) => {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const methodName = name || `${target.constructor.name}.${propertyKey}`;
    
    descriptor.value = function (...args: any[]) {
      return measureFunction(methodName, () => originalMethod.apply(this, args));
    };
    
    return descriptor;
  };
};
