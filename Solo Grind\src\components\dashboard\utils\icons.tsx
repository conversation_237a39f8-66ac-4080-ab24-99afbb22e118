import React from 'react';
import {
  Trophy,
  Target,
  Medal,
  Crown,
  Map,
  MapPin,
  Mountain,
  Zap,
  Sparkles,
  Star,
  Activity,
  BarChart3,
  TrendingUp,
  Calendar,
  Sunrise,
  Moon,
  LucideIcon
} from 'lucide-react';

// Icon mapping for achievements
export const getIconComponent = (iconName: string): React.ReactElement => {
  const iconMap: Record<string, LucideIcon> = {
    Trophy,
    Target,
    Medal,
    Crown,
    Map,
    MapPin,
    Mountain,
    Zap,
    Sparkles,
    Star,
    Activity,
    BarChart3,
    TrendingUp,
    Calendar,
    Sunrise,
    Moon,
  };

  const IconComponent = iconMap[iconName] || Trophy;
  return <IconComponent className="w-full h-full text-electric" />;
};
