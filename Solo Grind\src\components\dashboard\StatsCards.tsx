import { GlassCard } from '@/components/GlassCard';

interface StatCard {
  label: string;
  value: string;
  color: string;
}

interface StatsCardsProps {
  statCards: StatCard[];
}

export function StatsCards({ statCards }: StatsCardsProps) {
  return (
    <div className="mt-7 grid grid-cols-3 gap-4 px-4">
      {statCards.map((card) => (
        <GlassCard
          key={card.label}
          className={`stat-card bg-gradient-to-br ${card.color} !bg-clip-padding`}
        >
          <div className="stat-value">{card.value}</div>
          <div className="stat-label">{card.label}</div>
        </GlassCard>
      ))}
    </div>
  );
}
