// Database connectivity and CRUD operations test page
import { BottomNav } from '@/components/BottomNav';
import { useAuth } from '@/contexts/AuthContext';
import {
  TestControls,
  TestResultsList,
  UserInfo,
  useDatabaseTests
} from '@/components/database-test';

export default function DatabaseTest() {
  const { user } = useAuth();
  const { testResults, isRunningTests, runAllTests } = useDatabaseTests(user?.id);

  return (
    <>
      <div
        style={{ backgroundImage: "url('https://images.unsplash.com/photo-1470813740244-df37b8c1edcb')" }}
        className="fixed inset-0 bg-center bg-cover filter blur-sm scale-105"
      />
      <div className="relative z-10 pb-20 pt-6 min-h-screen bg-black/70">
        <div className="px-4 space-y-6">
          <h1 className="gradient-title text-2xl mb-4 text-center">
            Database Connectivity Test
          </h1>

          {/* User Info */}
          {user && <UserInfo user={user} />}

          {/* Test Controls */}
          <TestControls
            onRunTests={runAllTests}
            isRunningTests={isRunningTests}
            hasUser={!!user}
          />

          {/* Test Results */}
          <TestResultsList testResults={testResults} />
        </div>
        <BottomNav />
      </div>
    </>
  );
}
