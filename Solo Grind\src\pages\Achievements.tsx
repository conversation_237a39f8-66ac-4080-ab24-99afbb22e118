import * as React from "react";
import { GlassCard } from "@/components/GlassCard";
import { TierBadge } from "@/components/TierBadge";
import { BottomNav } from "@/components/BottomNav";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { useQuery } from "@tanstack/react-query";
import { Loader2 } from "lucide-react";
import * as LucideIcons from "lucide-react";

type Achievement = {
  id: string;
  name: string;
  title: string;
  description: string;
  icon_name: string;
  category: string;
  tier: string | null;
  requirement_type: string;
  requirement_value: number;
  points: number;
  unlocked: boolean;
  progress_value?: number;
};

// Helper function to get Lucide icon component by name
const getIconComponent = (iconName: string) => {
  const IconComponent = (LucideIcons as any)[iconName];
  return IconComponent ? <IconComponent size={36} className="text-electric" /> : <LucideIcons.Trophy size={36} className="text-electric" />;
};

// Helper function to get tier badge color
const getTierColor = (tier: string | null) => {
  switch (tier) {
    case 'Bronze': return 'from-amber-600 to-amber-800';
    case 'Silver': return 'from-gray-400 to-gray-600';
    case 'Gold': return 'from-yellow-400 to-yellow-600';
    case 'Platinum': return 'from-blue-400 to-blue-600';
    case 'Diamond': return 'from-purple-400 to-purple-600';
    default: return 'from-gray-500 to-gray-700';
  }
};

export default function Achievements() {
  const { user } = useAuth();

  // Fetch all achievements
  const { data: allAchievements, isLoading: isLoadingAchievements } = useQuery({
    queryKey: ['achievements'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('achievements')
        .select('*')
        .eq('is_active', true)
        .order('category', { ascending: true })
        .order('requirement_value', { ascending: true });

      if (error) {
        console.error('Error fetching achievements:', error);
        return [];
      }
      return data;
    },
  });

  // Fetch user's unlocked achievements
  const { data: userAchievements, isLoading: isLoadingUserAchievements } = useQuery({
    queryKey: ['user-achievements', user?.id],
    queryFn: async () => {
      if (!user) return [];
      const { data, error } = await supabase
        .from('user_achievements')
        .select('achievement_id, progress_value, unlocked_at')
        .eq('user_id', user.id);

      if (error) {
        console.error('Error fetching user achievements:', error);
        return [];
      }
      return data;
    },
    enabled: !!user,
  });

  // Combine achievements with user progress
  const achievementsWithProgress: Achievement[] = React.useMemo(() => {
    if (!allAchievements) return [];

    return allAchievements.map(achievement => {
      const userAchievement = userAchievements?.find(ua => ua.achievement_id === achievement.id);
      return {
        ...achievement,
        unlocked: !!userAchievement,
        progress_value: userAchievement?.progress_value,
      };
    });
  }, [allAchievements, userAchievements]);

  const isLoading = isLoadingAchievements || isLoadingUserAchievements;

  return (
    <>
      <div
        style={{ backgroundImage: "url('https://images.unsplash.com/photo-1470813740244-df37b8c1edcb')" }}
        className="fixed inset-0 bg-right bg-cover filter blur-sm scale-105"
      />
      <div className="relative z-10 pb-20 pt-6 min-h-screen bg-black/70">
        <div className="px-4 mb-2">
          <div className="flex items-center gap-3 mb-3">
            <h2 className="gradient-title text-xl">Achievements</h2>
            {!isLoading && (
              <span className="text-white/60 text-sm">
                {achievementsWithProgress.filter(a => a.unlocked).length} / {achievementsWithProgress.length}
              </span>
            )}
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center py-20">
              <Loader2 className="h-8 w-8 text-electric animate-spin" />
            </div>
          ) : (
            <div className="grid grid-cols-2 gap-3">
              {achievementsWithProgress.map((achievement) => (
                <GlassCard
                  key={achievement.id}
                  className={`flex flex-col items-center justify-center p-4 min-h-[140px] transition-all shadow-glow
                    ${!achievement.unlocked ? "opacity-60 grayscale-[0.8] border border-purple/30" : "border border-electric/30"}`}
                >
                  <div className="w-10 h-10 mb-2 flex items-center justify-center">
                    {getIconComponent(achievement.icon_name)}
                  </div>
                  <span className="font-bold text-xs mb-1 text-center text-white">
                    {achievement.title}
                  </span>
                  <span className="text-2xs text-white/60 text-center mb-2 px-1">
                    {achievement.description}
                  </span>

                  {achievement.tier && (
                    <div className={`px-2 py-1 rounded-full text-2xs font-bold bg-gradient-to-r ${getTierColor(achievement.tier)} text-white mb-1`}>
                      {achievement.tier}
                    </div>
                  )}

                  <div className="text-2xs text-electric font-semibold">
                    +{achievement.points} pts
                  </div>

                  {!achievement.unlocked && (
                    <span className="text-2xs text-purple mt-1">Locked</span>
                  )}

                  {achievement.unlocked && achievement.progress_value && (
                    <span className="text-2xs text-green-400 mt-1">
                      {achievement.progress_value.toFixed(1)}
                    </span>
                  )}
                </GlassCard>
              ))}
            </div>
          )}
        </div>
        <BottomNav />
      </div>
    </>
  );
}
